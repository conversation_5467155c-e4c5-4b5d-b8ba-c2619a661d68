/* eslint-disable @typescript-eslint/no-empty-object-type */

// Root DSLOpt Interface
export interface DSLOpt {
    fields?: DSLOptFields;
    model?: DSLOptModel;
    view?: DSLOptView;
}

// Fields Interfaces
export interface DSLOptFields {
    [x: string]: DSLOptFieldsDefaults;
}

export interface DSLOptFieldsDefaults {
    model?: DSLOptFieldsModel;
    view?: DSLOptFieldsView;
}

export interface DSLOptFieldsModel {
    access?: DSLOptFieldsModelAccess;
    active?: boolean;
    autoinsert?: boolean;
    fk?: boolean;
    default?: any;
    if?: DSLOptFieldsModelIf;
    ledger?: string | null;
    max?: number | string | null;
    min?: number | string | null;
    multi?: boolean;
    prefill?: string[];
    required?: boolean;
    required_all?: boolean;
    rounding?: number | null;
    save?: boolean;
    search?: string | null;
    query?: string | null;
    querytemplate?: string | null;
    source?: string | string[] | {} | null;
    source_order?: string[] | null;
    sourceid?: string | null;
    track?: boolean;
    sourcefilter?: DSLOptFieldsModelSourceFilter;
    subfields?: DSLOptFieldsModelSubfields;
    subfields_sort?: string[] | number[] | null;
    template?: string | null;
    transform?: any[];
    transform_filter?: any[];
    transform_post?: any[];
    type:
        | ""
        | "date"
        | "datetime"
        | "decimal"
        | "image"
        | "int"
        | "json"
        | "xml"
        | "password"
        | "subform"
        | "text"
        | "time"
        | "color"
        | null;
    validate?: any[];
    dynamic?: DSLOptFieldsModelDynamic;
}

export interface DSLOptFieldsModelAccess {
    read?: string[];
    if?: string | null;
    write?: string[];
}

export interface DSLOptFieldsModelDynamic {
    source?: string | null;
    type?: "" | "text" | null;
    query?: string | null;
}

export interface DSLOptFieldsModelIf {
    [x: string]: DSLOptFieldsModelIfDefaults;
}

export interface DSLOptFieldsModelIfDefaults {
    fields?: string[];
    note?: string | null;
    sections?: string[];
    require_fields?: string[];
    hide_fields?: string[];
    hide_sections?: string[];
    require_any?: string[];
    field_warning?: {} | null;
    highlight?: string | null;
    trigger?: string[];
    readonly?: DSLOptFieldsModelIfReadonly;
    prefill?: {} | null;
    form_label?: string | null;
    field_label?: {} | null;
}

export interface DSLOptFieldsModelIfReadonly {
    fields?: string[];
    sections?: string[];
}

export interface DSLOptFieldsModelSourceFilter {
    [x: string]: DSLOptFieldsModelSourceFilterDefaults;
}

export interface DSLOptFieldsModelSourceFilterDefaults {
    dynamic?: number | string | null;
    static?: number | string | string[] | null;
    source?: string[] | null;
    default?: number | string | null;
}

export interface DSLOptFieldsModelSubfields {
    [x: string]: DSLOptFieldsModelSubfieldsDefaults;
}

export interface DSLOptFieldsModelSubfieldsDefaults {
    label?: string | null;
    style?: {} | null;
    readonly?: boolean;
    required?: boolean;
    multi?: boolean;
    offscreen?: boolean;
    source?: string | string[] | {} | null;
    dynamic?: string | null;
    format:
        | ""
        | "0,0.[000000]"
        | "$0,0.[000000]"
        | "$0,0.00"
        | "$0,0.0000"
        | "0,0"
        | "percent"
        | "us_phone"
        | null;
    sourceid?: string | null;
    sourcefilter?: DSLOptFieldsModelSubfieldsSourceFilter;
    type:
        | ""
        | "date"
        | "datenow"
        | "datetime"
        | "decimal"
        | "int"
        | "text"
        | "time"
        | "timenow"
        | "timestamp"
        | "checkbox"
        | "area"
        | null;
    class?: string | null;
}

export interface DSLOptFieldsModelSubfieldsSourceFilter {
    [x: string]: DSLOptFieldsModelSubfieldsSourceFilterDefaults;
}

export interface DSLOptFieldsModelSubfieldsSourceFilterDefaults {
    dynamic?: number | string | null;
    static?: number | string | string[] | null;
    source?: string[] | null;
    default?: number | string | null;
}

export interface DSLOptFieldsView {
    form_link_enabled?: boolean;
    class?: string | null;
    embed?: DSLOptFieldsViewEmbed;
    add_preset?: {} | null;
    control:
        | ""
        | "area"
        | "barcode"
        | "checkbox"
        | "esign"
        | "file"
        | "grid"
        | "inline"
        | "input"
        | "link"
        | "radio"
        | "paycard"
        | "picker"
        | "select"
        | "subform"
        | "xml"
        | "embedded_table"
        | "raw"
        | "json"
        | null;
    columns?: number | string | null;
    findfilter?: string | string[] | null;
    findmulti?: boolean;
    findunique?: boolean;
    findrange?: boolean;
    findwildcard?: boolean;
    format:
        | ""
        | "hic"
        | "ssn"
        | "us_phone"
        | "us_zip"
        | "url"
        | "0,0.[000000]"
        | "$0,0.[000000]"
        | "$0,0.00"
        | "$0,0.0000"
        | "0,0"
        | "percent"
        | null;
    grid?: DSLOptFieldsViewGrid;
    requireall_bypass?: boolean;
    requireif_bypass?: boolean;
    highlight?: string | null;
    label?: string | null;
    max_count?: number | null;
    note?: string | null;
    reference?: string | string[] | null;
    offscreen?: boolean;
    readonly?: boolean;
    template?: string | null;
    transform?: any[];
    validate?: any[];
    _meta?: string | string[] | {} | null;
}

export interface DSLOptFieldsViewEmbed {
    request_type?: "GET" | "POST";
    add_form?: string | null;
    form?: string | null;
    query?: string | null;
    selectable?: boolean;
    add_preset?: {} | null;
}

export interface DSLOptFieldsViewGrid {
    add?: "" | "flyout" | "inline" | "none" | null;
    rank?: "" | "none" | "local" | "global" | null;
    hide_cardmenu?: boolean;
    copy?: string[];
    edit?: boolean;
    delete?: boolean;
    fields?: string[];
    label?: string[];
    split?: boolean;
    splitif?: {} | null;
    deleteif?: {} | null;
    text_trim?: number | null;
    tooltip?: string[];
    width?: string[] | number[];
    selectall?: boolean;
    allow_read_wo_id?: boolean;
    subfields?: string[];
    subfields_label?: string[];
    subfields_width?: string[] | number[];
}

// Model Interfaces
export interface DSLOptModel {
    access?: DSLOptModelAccess;
    bundle?: string[];
    collections?: string[];
    indexes?: DSLOptModelIndexes;
    large?: boolean;
    ledger?: string | null;
    name?: string | string[] | null;
    prefill?: DSLOptModelPrefill;
    reportable?: boolean;
    required_if?: string | null;
    save?: boolean;
    sections_group?: any[];
    sections?: DSLOptModelSections;
    sections_order?: string[] | null;
    sync_mode?: "" | "full" | "mixed" | "none" | null;
    transform?: any[];
    transform_filter?: any[];
    transform_post?: any[];
    validate?: any[];
}

export interface DSLOptModelAccess {
    create?: string[];
    create_all?: string[];
    read?: string[];
    read_all?: string[];
    update?: string[];
    update_all?: string[];
    delete?: string[];
    request?: string[];
    review?: string[];
    rereview?: string[];
    write?: string[];
}

export interface DSLOptModelIndexes {
    gin?: (string | string[])[];
    fulltext?: (string | string[])[];
    lower?: (string | string[])[];
    many?: (string | string[])[];
    unique?: (string | string[])[];
}

export interface DSLOptModelPrefill {
    [x: string]: DSLOptModelPrefillDefaults;
}

export interface DSLOptModelPrefillDefaults {
    filter?: {} | null;
    link?: {} | null;
    max?: string | null;
    min?: string | null;
}

export interface DSLOptModelSections {
    [x: string]: DSLOptModelSectionsDefaults;
}

export interface DSLOptModelSectionsDefaults {
    fields?: string[];
    tab?: string | null;
    tab_toggle?: boolean;
    hide_header?: boolean;
    indent?: boolean;
    compact?: boolean;
    group?: {} | null;
    note?: string | null;
    area?: string | null;
    prefill?: string | null;
    modal?: boolean;
}

// View Interfaces
export interface DSLOptView {
    block?: DSLOptViewBlock;
    comment?: string | null;
    dimensions?: {} | null;
    find?: DSLOptViewFind;
    grid?: DSLOptViewGrid;
    hide_header?: boolean;
    hide_cardmenu?: boolean;
    icon?: string | null;
    label?: string | null;
    max_rows?: number | null;
    open?: "" | "read" | "edit" | null;
    transform?: any[];
    validate?: any[];
    reference?: string | any[] | null;
}

export interface DSLOptViewBlock {
    print?: DSLOptViewBlockPrint;
    update?: DSLOptViewBlockUpdate;
    validate?: any[];
}

export interface DSLOptViewBlockPrint {
    if?: string | null;
    except?: string[];
}

export interface DSLOptViewBlockUpdate {
    if?: string | null;
    except?: string[];
}

export interface DSLOptViewFind {
    advanced?: string[];
    basic?: string[];
}

export interface DSLOptViewGrid {
    fields?: string[];
    width?: string[] | number[] | null;
    sort?: string[];
    label?: string[];
    pivot?: string | null;
    aggregate?: DSLOptViewGridAggregate;
    group_by?: string | null;
    style?: DSLOptViewGridStyle;
    menu?: string[];
    hide_columns?: string[];
}

export interface DSLOptViewGridAggregate {
    [x: string]: DSLOptViewGridAggregateDefaults;
}

export interface DSLOptViewGridAggregateDefaults {
    field?: string | null;
    func?: string | null;
}

export interface DSLOptViewGridStyle {
    [x: string]: DSLOptViewGridStyleDefaults;
}

export interface DSLOptViewGridStyleDefaults {
    style?: {} | null;
}

export default DSLOpt;
