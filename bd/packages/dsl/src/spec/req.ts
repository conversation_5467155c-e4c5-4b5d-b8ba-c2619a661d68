/* eslint-disable @typescript-eslint/no-empty-object-type */

// Root DSLReq Interface
export interface DSLReq {
    fields: DSLReqFields;
    model: DSLReqModel;
    view: DSLReqView;
}

// Fields Interfaces
export interface DSLReqFields {
    [x: string]: DSLReqFieldsDefaults;
}

export interface DSLReqFieldsDefaults {
    model: DSLReqFieldsModel;
    view: DSLReqFieldsView;
}

export interface DSLReqFieldsModel {
    access: DSLReqFieldsModelAccess;
    active: boolean;
    autoinsert: boolean;
    fk: boolean;
    default: any;
    if: DSLReqFieldsModelIf;
    ledger: string | null;
    max: number | string | null;
    min: number | string | null;
    multi: boolean;
    prefill: string[];
    required: boolean;
    required_all: boolean;
    rounding: number | null;
    save: boolean;
    search: string | null;
    query: string | null;
    querytemplate: string | null;
    source: string | string[] | {} | null;
    source_order: string[] | null;
    sourceid: string | null;
    track: boolean;
    sourcefilter: DSLReqFieldsModelSourceFilter;
    subfields: DSLReqFieldsModelSubfields;
    subfields_sort: string[] | number[] | null;
    template: string | null;
    transform: any[];
    transform_filter: any[];
    transform_post: any[];
    type:
        | ""
        | "date"
        | "datetime"
        | "decimal"
        | "image"
        | "int"
        | "json"
        | "xml"
        | "password"
        | "subform"
        | "text"
        | "time"
        | "color"
        | null;
    validate: any[];
    dynamic: DSLReqFieldsModelDynamic;
}

export interface DSLReqFieldsModelAccess {
    read: string[];
    if: string | null;
    write: string[];
}

export interface DSLReqFieldsModelDynamic {
    source: string | null;
    type: "" | "text" | null;
    query: string | null;
}

export interface DSLReqFieldsModelIf {
    [x: string]: DSLReqFieldsModelIfDefaults;
}

export interface DSLReqFieldsModelIfDefaults {
    fields: string[];
    note: string | null;
    sections: string[];
    require_fields: string[];
    hide_fields: string[];
    hide_sections: string[];
    require_any: string[];
    field_warning: {} | null;
    highlight: string | null;
    trigger: string[];
    readonly: DSLReqFieldsModelIfReadonly;
    prefill: {} | null;
    form_label: string | null;
    field_label: {} | null;
}

export interface DSLReqFieldsModelIfReadonly {
    fields: string[];
    sections: string[];
}

export interface DSLReqFieldsModelSourceFilter {
    [x: string]: DSLReqFieldsModelSourceFilterDefaults;
}

export interface DSLReqFieldsModelSourceFilterDefaults {
    dynamic: number | string | null;
    static: number | string | string[] | null;
    source: string[] | null;
    default: number | string | null;
}

export interface DSLReqFieldsModelSubfields {
    [x: string]: DSLReqFieldsModelSubfieldsDefaults;
}

export interface DSLReqFieldsModelSubfieldsDefaults {
    label: string | null;
    style: {} | null;
    readonly: boolean;
    required: boolean;
    multi: boolean;
    offscreen: boolean;
    source: string | string[] | {} | null;
    dynamic: string | null;
    format:
        | ""
        | "0,0.[000000]"
        | "$0,0.[000000]"
        | "$0,0.00"
        | "$0,0.0000"
        | "0,0"
        | "percent"
        | "us_phone"
        | null;
    sourceid: string | null;
    sourcefilter: DSLReqFieldsModelSubfieldsSourceFilter;
    type:
        | ""
        | "date"
        | "datenow"
        | "datetime"
        | "decimal"
        | "int"
        | "text"
        | "time"
        | "timenow"
        | "timestamp"
        | "checkbox"
        | "area"
        | null;
    class: string | null;
}

export interface DSLReqFieldsModelSubfieldsSourceFilter {
    [x: string]: DSLReqFieldsModelSubfieldsSourceFilterDefaults;
}

export interface DSLReqFieldsModelSubfieldsSourceFilterDefaults {
    dynamic: number | string | null;
    static: number | string | string[] | null;
    source: string[] | null;
    default: number | string | null;
}

export interface DSLReqFieldsView {
    form_link_enabled: boolean;
    class: string | null;
    embed: DSLReqFieldsViewEmbed;
    add_preset: {} | null;
    control:
        | ""
        | "area"
        | "barcode"
        | "checkbox"
        | "esign"
        | "file"
        | "grid"
        | "inline"
        | "input"
        | "link"
        | "radio"
        | "paycard"
        | "picker"
        | "select"
        | "subform"
        | "xml"
        | "embedded_table"
        | "raw"
        | "json"
        | null;
    columns: number | string | null;
    findfilter: string | string[] | null;
    findmulti: boolean;
    findunique: boolean;
    findrange: boolean;
    findwildcard: boolean;
    format:
        | ""
        | "hic"
        | "ssn"
        | "us_phone"
        | "us_zip"
        | "url"
        | "0,0.[000000]"
        | "$0,0.[000000]"
        | "$0,0.00"
        | "$0,0.0000"
        | "0,0"
        | "percent"
        | null;
    grid: DSLReqFieldsViewGrid;
    requireall_bypass: boolean;
    requireif_bypass: boolean;
    highlight: string | null;
    label: string | null;
    max_count: number | null;
    note: string | null;
    reference: string | string[] | null;
    offscreen: boolean;
    readonly: boolean;
    template: string | null;
    transform: any[];
    validate: any[];
    _meta: string | string[] | {} | null;
}

export interface DSLReqFieldsViewEmbed {
    request_type: "GET" | "POST";
    add_form: string | null;
    form: string | null;
    query: string | null;
    selectable: boolean;
    add_preset: {} | null;
}

export interface DSLReqFieldsViewGrid {
    add: "" | "flyout" | "inline" | "none" | null;
    rank: "" | "none" | "local" | "global" | null;
    hide_cardmenu: boolean;
    copy: string[];
    edit: boolean;
    delete: boolean;
    fields: string[];
    label: string[];
    split: boolean;
    splitif: {} | null;
    deleteif: {} | null;
    text_trim: number | null;
    tooltip: string[];
    width: string[] | number[];
    selectall: boolean;
    allow_read_wo_id: boolean;
    subfields: string[];
    subfields_label: string[];
    subfields_width: string[] | number[];
}

// Model Interfaces
export interface DSLReqModel {
    access: DSLReqModelAccess;
    bundle: string[];
    collections: string[];
    indexes: DSLReqModelIndexes;
    large: boolean;
    ledger: string | null;
    name: string | string[] | null;
    prefill: DSLReqModelPrefill;
    reportable: boolean;
    required_if: string | null;
    save: boolean;
    sections_group: any[];
    sections: DSLReqModelSections;
    sections_order: string[] | null;
    sync_mode: "" | "full" | "mixed" | "none" | null;
    transform: any[];
    transform_filter: any[];
    transform_post: any[];
    validate: any[];
}

export interface DSLReqModelAccess {
    create: string[];
    create_all: string[];
    read: string[];
    read_all: string[];
    update: string[];
    update_all: string[];
    delete: string[];
    request: string[];
    review: string[];
    rereview: string[];
    write: string[];
}

export interface DSLReqModelIndexes {
    gin: (string | string[])[];
    fulltext: (string | string[])[];
    lower: (string | string[])[];
    many: (string | string[])[];
    unique: (string | string[])[];
}

export interface DSLReqModelPrefill {
    [x: string]: DSLReqModelPrefillDefaults;
}

export interface DSLReqModelPrefillDefaults {
    filter: {} | null;
    link: {} | null;
    max: string | null;
    min: string | null;
}

export interface DSLReqModelSections {
    [x: string]: DSLReqModelSectionsDefaults;
}

export interface DSLReqModelSectionsDefaults {
    fields: string[];
    tab: string | null;
    tab_toggle: boolean;
    hide_header: boolean;
    indent: boolean;
    compact: boolean;
    group: {} | null;
    note: string | null;
    area: string | null;
    prefill: string | null;
    modal: boolean;
}

// View Interfaces
export interface DSLReqView {
    block: DSLReqViewBlock;
    comment: string | null;
    dimensions: {} | null;
    find: DSLReqViewFind;
    grid: DSLReqViewGrid;
    hide_header: boolean;
    hide_cardmenu: boolean;
    icon: string | null;
    label: string | null;
    max_rows: number | null;
    open: "" | "read" | "edit" | null;
    transform: any[];
    validate: any[];
    reference: string | any[] | null;
}

export interface DSLReqViewBlock {
    print: DSLReqViewBlockPrint;
    update: DSLReqViewBlockUpdate;
    validate: any[];
}

export interface DSLReqViewBlockPrint {
    if: string | null;
    except: string[];
}

export interface DSLReqViewBlockUpdate {
    if: string | null;
    except: string[];
}

export interface DSLReqViewFind {
    advanced: string[];
    basic: string[];
}

export interface DSLReqViewGrid {
    fields: string[];
    width: string[] | number[] | null;
    sort: string[];
    label: string[];
    pivot: string | null;
    aggregate: DSLReqViewGridAggregate;
    group_by: string | null;
    style: DSLReqViewGridStyle;
    menu: string[];
    hide_columns: string[];
}

export interface DSLReqViewGridAggregate {
    [x: string]: DSLReqViewGridAggregateDefaults;
}

export interface DSLReqViewGridAggregateDefaults {
    field: string | null;
    func: string | null;
}

export interface DSLReqViewGridStyle {
    [x: string]: DSLReqViewGridStyleDefaults;
}

export interface DSLReqViewGridStyleDefaults {
    style: {} | null;
}

export default DSLReq;
