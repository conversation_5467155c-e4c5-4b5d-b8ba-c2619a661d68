import type { DSLReq as DSL } from "@clara/dsl/spec/req";
import type * as D from "@clara/dsl/spec/req";

// each node will have these + parent
export interface PathRoot<S extends DSL = DSL> {
    readonly ˆpath: string;
    readonly ˆroot: DSLForm<S>;
    readonly ˆtype: string;
}

export interface DSLForm<S extends DSL = DSL>
    extends Omit<D.DSLReq, "fields" | "model" | "view">,
        PathRoot<S> {
    readonly ˆparent: DSLForm<S>;

    fields: DSLFormFields<S, S["fields"]>;
    model: DSLFormModel<S>;
    view: DSLFormView<S>;

    ˆrows(): DSLForm<S>[];
    ˆtoJSON(): S;
    ˆisRoot(): boolean;
}

export type DSLFormFields<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
> = F &
    D.DSLReqFields &
    DSLMethodFields<S, F> & {
        [K in keyof F]: DSLFormFieldsDefaults<S, F> & F[K];
    };

export interface DSLMethodFields<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
> extends PathRoot<S> {
    readonly ˆparent: DSLForm<S>;

    ˆgetField(name: keyof F & string): DSLFormFields<S, F>;
    ˆgetAllValues(): { [P in keyof S["fields"]]?: unknown };
    ˆsetValues(obj: { [P in keyof S["fields"]]?: unknown }): void;
    ˆclearValues?(): void;
    ˆisFields(): boolean;
}

export interface DSLFormFieldsDefaults<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
> extends Omit<D.DSLReqFieldsDefaults, "model" | "view">,
        PathRoot<S> {
    readonly ˆparent: DSLFormFields<S, F>;

    model: DSLFormFieldsModel<S, F>;
    view: DSLFormFieldsView<S, F>;

    ˆgetValue(): any;
    ˆsetValue(v: any): any;
    ˆgetLabel(): string;
    ˆclear(): void;
    ˆisFieldsDefaults(): boolean;
}

export interface DSLFormFieldsModel<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
> extends Omit<
            D.DSLReqFieldsModel,
            "access" | "dynamic" | "if" | "sourcefilter" | "subfields"
        >,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsDefaults<S, F>;

    access: DSLFormFieldsModelAccess<S, F>;
    dynamic: DSLFormFieldsModelDynamic<S, F>;
    if: DSLFormFieldsModelIf<S, F, F[keyof F & string]["model"]["if"]>;
    sourcefilter: DSLFormFieldsModelSourceFilter<
        S,
        F,
        F[keyof F & string]["model"]["sourcefilter"]
    >;
    subfields: DSLFormFieldsModelSubfields<
        S,
        F,
        F[keyof F & string]["model"]["subfields"]
    >;

    ˆisFieldsModel(): boolean;
}

export interface DSLFormFieldsModelAccess<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
> extends D.DSLReqFieldsModelAccess,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModel<S, F>;

    ˆisFieldsModelAccess(): boolean;
}

export interface DSLFormFieldsModelDynamic<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
> extends D.DSLReqFieldsModelDynamic,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModel<S, F>;

    ˆisFieldsModelDynamic(): boolean;
}

export type DSLFormFieldsModelIf<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends D.DSLReqFieldsModelIf = D.DSLReqFieldsModelIf,
> = F &
    DSLMethodFieldsModelIf<S> & {
        [K in keyof F]: DSLFormFieldsModelIfDefaults<S, F, G> & F[K];
    };

export interface DSLMethodFieldsModelIf<S extends DSL = DSL>
    extends PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModel<S>;

    ˆisFieldsModelIf(): boolean;
}

export interface DSLFormFieldsModelIfDefaults<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends D.DSLReqFieldsModelIf = D.DSLReqFieldsModelIf,
> extends Omit<D.DSLReqFieldsModelIfDefaults, "readonly">,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModelIf<S, F, G>;

    readonly: DSLFormFieldsModelIfReadonly<S, F, G>;

    ˆisFieldsModelIfDefaults(): boolean;
}

export interface DSLFormFieldsModelIfReadonly<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends D.DSLReqFieldsModelIf = D.DSLReqFieldsModelIf,
> extends D.DSLReqFieldsModelIfReadonly,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModelIfDefaults<S, F, G>;

    ˆisFieldsModelIfReadonly(): boolean;
}

export type DSLFormFieldsModelSourceFilter<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends D.DSLReqFieldsModelSourceFilter = D.DSLReqFieldsModelSourceFilter,
> = F &
    DSLMethodFieldsModelSourceFilter<S> & {
        [K in keyof F]: DSLFormFieldsModelSourceFilterDefaults<S, F, G> & F[K];
    };

export interface DSLMethodFieldsModelSourceFilter<S extends DSL = DSL>
    extends PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModel<S>;

    ˆisFieldsModelSourceFilter(): boolean;
}

export interface DSLFormFieldsModelSourceFilterDefaults<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends D.DSLReqFieldsModelSourceFilter = D.DSLReqFieldsModelSourceFilter,
> extends D.DSLReqFieldsModelSourceFilterDefaults,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModelSourceFilter<S, F, G>;

    ˆisFieldsModelSourceFilterDefaults(): boolean;
}

export type DSLFormFieldsModelSubfields<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends D.DSLReqFieldsModelSubfields = D.DSLReqFieldsModelSubfields,
> = F &
    DSLMethodFieldsModelSubfields<S> & {
        [K in keyof F]: DSLFormFieldsModelSubfieldsDefaults<S, F, G> & F[K];
    };

export interface DSLMethodFieldsModelSubfields<S extends DSL = DSL>
    extends PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModel<S>;

    ˆisFieldsModelSubfields(): boolean;
}

export interface DSLFormFieldsModelSubfieldsDefaults<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends D.DSLReqFieldsModelSubfields = D.DSLReqFieldsModelSubfields,
> extends Omit<D.DSLReqFieldsModelSubfieldsDefaults, "sourcefilter">,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModelSubfields<S>;
    sourcefilter: DSLFormFieldsModelSubfieldsSourceFilter<S, F, G>;

    ˆisFieldsModelSubfieldsDefaults(): boolean;
}

export type DSLFormFieldsModelSubfieldsSourceFilter<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends D.DSLReqFieldsModelSubfields = D.DSLReqFieldsModelSubfields,
> = F &
    DSLMethodFieldsModelSubfieldsSourceFilter<S, F, G[> & {
        [K in keyof F]: DSLFormFieldsModelSubfieldsSourceFilterDefaults<
            S,
            F,
            G
        > &
            F[K];
    };

export interface DSLMethodFieldsModelSubfieldsSourceFilter<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends
        D.DSLReqFieldsModelSubfieldsSourceFilter = D.DSLReqFieldsModelSubfieldsSourceFilter,
> extends PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModelSubfieldsDefaults<S, F, G>;

    ˆisFieldsModelSubfieldsSourceFilter(): boolean;
}

export interface DSLFormFieldsModelSubfieldsSourceFilterDefaults<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
    G extends
        D.DSLReqFieldsModelSubfieldsSourceFilter = D.DSLReqFieldsModelSubfieldsSourceFilter,
> extends D.DSLReqFieldsModelSubfieldsSourceFilterDefaults,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsModelSubfieldsSourceFilter<S, F, G>;

    ˆisFieldsModelSubfieldsSourceFilterDefaults(): boolean;
}

export interface DSLFormFieldsView<
    S extends DSL = DSL,
    F extends D.DSLReqFields = D.DSLReqFields,
> extends Omit<D.DSLReqFieldsView, "embed" | "grid">,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsDefaults<S, F>;

    embed: DSLFormFieldsViewEmbed<S, F>;
    grid: DSLFormFieldsViewGrid<S, F>;

    ˆisFieldsView(): boolean;
}

export interface DSLFormFieldsViewEmbed<
    S extends DSL = DSL,
    F extends D.DSLReqFieldsView = D.DSLReqFieldsView,
> extends D.DSLReqFieldsViewEmbed,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsView<S, F>;

    ˆisFieldsViewEmbed(): boolean;
}

export interface DSLFormFieldsViewGrid<
    S extends DSL = DSL,
    F extends D.DSLReqFieldsView = D.DSLReqFieldsView,
> extends D.DSLReqFieldsViewGrid,
        PathRoot<S> {
    readonly ˆparent: DSLFormFieldsView<S, F>;

    ˆisFieldsViewGrid(): boolean;
}

export interface DSLFormModel<S extends DSL = DSL>
    extends Omit<D.DSLReqModel, "access" | "indexes" | "prefill" | "sections">,
        PathRoot<S> {
    readonly ˆparent: DSLForm<S>;

    access: DSLFormModelAccess<S>;
    indexes: DSLFormModelIndexes<S>;
    prefill: DSLFormModelPrefill<S, S["model"]["prefill"]>;
    sections: DSLFormModelSections<S, S["model"]["sections"]>;

    ˆisModel(): boolean;
}

export interface DSLFormModelAccess<S extends DSL = DSL>
    extends D.DSLReqModelAccess,
        PathRoot<S> {
    readonly ˆparent: DSLFormModel<S>;

    ˆisModelAccess(): boolean;
}

export interface DSLFormModelIndexes<S extends DSL = DSL>
    extends D.DSLReqModelIndexes,
        PathRoot<S> {
    readonly ˆparent: DSLFormModel<S>;

    ˆisModelIndexes(): boolean;
}

export type DSLFormModelPrefill<
    S extends DSL = DSL,
    F extends D.DSLReqModelPrefill = D.DSLReqModelPrefill,
> = F &
    DSLMethodModelPrefill<S> & {
        [K in keyof F]: DSLFormModelPrefillDefaults<S, F> & F[K];
    };

export interface DSLMethodModelPrefill<S extends DSL = DSL>
    extends PathRoot<S> {
    readonly ˆparent: DSLFormModel<S>;

    ˆisModelPrefill(): boolean;
}

export interface DSLFormModelPrefillDefaults<
    S extends DSL = DSL,
    F extends D.DSLReqModelPrefill = D.DSLReqModelPrefill,
> extends D.DSLReqModelPrefillDefaults,
        PathRoot<S> {
    readonly ˆparent: DSLFormModelPrefill<S, F>;

    ˆisModelPrefillDefaults(): boolean;
}

export type DSLFormModelSections<
    S extends DSL = DSL,
    F extends D.DSLReqModelSections = D.DSLReqModelSections,
> = F &
    DSLMethodModelSections<S> & {
        [K in keyof F]: DSLFormModelSectionsDefaults<S, F> & F[K];
    };

export interface DSLMethodModelSections<S extends DSL = DSL>
    extends PathRoot<S> {
    readonly ˆparent: DSLFormModel<S>;

    ˆisModelSections(): boolean;
}

export interface DSLFormModelSectionsDefaults<
    S extends DSL = DSL,
    F extends D.DSLReqModelSections = D.DSLReqModelSections,
> extends D.DSLReqModelSectionsDefaults,
        PathRoot<S> {
    readonly ˆparent: DSLFormModelSections<S, F>;

    ˆisModelSectionsDefaults(): boolean;
}

export interface DSLFormView<S extends DSL = DSL>
    extends Omit<D.DSLReqView, "block" | "find" | "grid">,
        PathRoot<S> {
    readonly ˆparent: DSLForm<S>;

    block: DSLFormViewBlock<S>;
    find: DSLFormViewFind<S>;
    grid: DSLFormViewGrid<S>;

    ˆisView(): boolean;
}

export interface DSLFormViewBlock<S extends DSL = DSL>
    extends Omit<D.DSLReqViewBlock, "print" | "update">,
        PathRoot<S> {
    readonly ˆparent: DSLFormView<S>;

    print: DSLFormViewBlockPrint<S>;
    update: DSLFormViewBlockUpdate<S>;

    ˆisViewBlock(): boolean;
}

export interface DSLFormViewBlockPrint<S extends DSL = DSL>
    extends D.DSLReqViewBlockPrint,
        PathRoot<S> {
    readonly ˆparent: DSLFormViewBlock<S>;

    ˆisViewBlockPrint(): boolean;
}

export interface DSLFormViewBlockUpdate<S extends DSL = DSL>
    extends D.DSLReqViewBlockUpdate,
        PathRoot<S> {
    readonly ˆparent: DSLFormViewBlock<S>;

    ˆisViewBlockUpdate(): boolean;
}

export interface DSLFormViewFind<S extends DSL = DSL>
    extends D.DSLReqViewFind,
        PathRoot<S> {
    readonly ˆparent: DSLFormView<S>;

    ˆisViewFind(): boolean;
}

export interface DSLFormViewGrid<S extends DSL = DSL>
    extends Omit<D.DSLReqViewGrid, "aggregate" | "style">,
        PathRoot<S> {
    readonly ˆparent: DSLFormView<S>;

    aggregate: DSLFormViewGridAggregate<S, S["view"]["grid"]["aggregate"]>;
    style: DSLFormViewGridStyle<S, S["view"]["grid"]["style"]>;

    ˆisViewGrid(): boolean;
}

export type DSLFormViewGridAggregate<
    S extends DSL = DSL,
    F extends D.DSLReqViewGridAggregate = D.DSLReqViewGridAggregate,
> = F &
    DSLMethodViewGridAggregate<S> & {
        [K in keyof F]: DSLFormViewGridAggregateDefaults<S, F> & F[K];
    };

export interface DSLMethodViewGridAggregate<S extends DSL = DSL>
    extends PathRoot<S> {
    readonly ˆparent: DSLFormViewGrid<S>;

    ˆisViewGridAggregate(): boolean;
}

export interface DSLFormViewGridAggregateDefaults<
    S extends DSL = DSL,
    F extends D.DSLReqViewGridAggregate = D.DSLReqViewGridAggregate,
> extends D.DSLReqViewGridAggregateDefaults,
        PathRoot<S> {
    readonly ˆparent: DSLFormViewGridAggregate<S, F>;

    ˆisViewGridAggregateDefaults(): boolean;
}

export type DSLFormViewGridStyle<
    S extends DSL = DSL,
    F extends D.DSLReqViewGridStyle = D.DSLReqViewGridStyle,
> = F &
    DSLMethodViewGridStyle<S> & {
        [K in keyof F]: DSLFormViewGridStyleDefaults<S, F> & F[K];
    };

export interface DSLMethodViewGridStyle<S extends DSL = DSL>
    extends PathRoot<S> {
    readonly ˆparent: DSLFormViewGrid<S>;

    ˆisViewGridStyle(): boolean;
}

export interface DSLFormViewGridStyleDefaults<
    S extends DSL = DSL,
    F extends D.DSLReqViewGridStyle = D.DSLReqViewGridStyle,
> extends D.DSLReqViewGridStyleDefaults,
        PathRoot<S> {
    readonly ˆparent: DSLFormViewGridStyle<S, F>;

    ˆisViewGridStyleDefaults(): boolean;
}

type DSLContext<S extends DSL = DSL> = {
    path: string;
    type: DSLMethodsKeys;
    store: WeakMap<object, Record<string, unknown>>;
    root?: DSLForm<S>;
    parent?: any;
    parentObject?: object;
    formName: string;
};

type MethodCtx<S extends DSL = DSL> = {
    target: any;
    storeKey: object;
    root: DSLForm<S>;
    parent: any;
    dslFormDeep: <T extends DSL>(obj: any, context: DSLContext<T>) => any;
    fieldName?: string;
    store: WeakMap<object, Record<string, unknown>>;
    formName: string;
};

const DSLMethods: Record<
    string,
    Record<string, (ctx: MethodCtx<any>) => unknown>
> = {
    "": {
        ˆisRoot: () => () => true,
        ˆrows:
            ({ target }) =>
            () =>
                target.rows ?? [],
        ˆtoJSON:
            ({ target }) =>
            () =>
                structuredClone(target),
    },
    fields: {
        ˆisFields: () => () => true,
        ˆgetField:
            ({ target, root, parent, dslFormDeep, store, formName }) =>
            (name: string) => {
                const fieldSpec = target[name];
                return dslFormDeep(fieldSpec, {
                    path: `${parent.ˆpath}.${name}`,
                    type: "fields.*",
                    store,
                    root,
                    parent,
                    parentObject: target,
                    formName,
                });
            },
        ˆgetAllValues:
            ({ storeKey, store }) =>
            () =>
                store.get(storeKey) ?? {},
        ˆsetValues:
            ({ storeKey, store }) =>
            (o: Record<string, unknown>) =>
                store.set(storeKey, { ...(store.get(storeKey) ?? {}), ...o }),
        ˆclearValues:
            ({ storeKey, store }) =>
            () =>
                store.set(storeKey, {}),
    },
    "fields.*": {
        ˆisFieldsDefaults: () => () => true,
        ˆgetValue:
            ({ target, fieldName, storeKey, store }) =>
            () =>
                (store.get(storeKey) ?? {})[fieldName!] ??
                target.model?.default,
        ˆsetValue:
            ({ storeKey, fieldName, store }) =>
            (v: unknown) => {
                const s = store.get(storeKey) ?? {};
                s[fieldName!] = v;
                store.set(storeKey, s);
                return v;
            },
        ˆgetLabel:
            ({ target, fieldName }) =>
            () =>
                target.view?.label ?? fieldName,
        ˆclear:
            ({ storeKey, fieldName, store }) =>
            () => {
                const s = store.get(storeKey) ?? {};
                delete s[fieldName!];
                store.set(storeKey, s);
            },
    },
    "fields.*.model": { ˆisFieldsModel: () => () => true },
    "fields.*.model.access": { ˆisFieldsModelAccess: () => () => true },
    "fields.*.model.dynamic": { ˆisFieldsModelDynamic: () => () => true },
    "fields.*.model.if.*": { ˆisFieldsModelIfDefaults: () => () => true },
    "fields.*.model.if.*.readonly": {
        ˆisFieldsModelIfReadonly: () => () => true,
    },
    "fields.*.model.sourcefilter.*": {
        ˆisFieldsModelSourceFilterDefaults: () => () => true,
    },
    "fields.*.model.subfields.*": {
        ˆisFieldsModelSubfieldsDefaults: () => () => true,
    },
    "fields.*.model.subfields.*.sourcefilter.*": {
        ˆisFieldsModelSubfieldsSourceFilterDefaults: () => () => true,
    },
    "fields.*.view": { ˆisFieldsView: () => () => true },
    "fields.*.view.embed": { ˆisFieldsViewEmbed: () => () => true },
    "fields.*.view.grid": { ˆisFieldsViewGrid: () => () => true },
    model: { ˆisModel: () => () => true },
    "model.access": { ˆisModelAccess: () => () => true },
    "model.indexes": { ˆisModelIndexes: () => () => true },
    "model.prefill.*": { ˆisModelPrefillDefaults: () => () => true },
    "model.sections.*": { ˆisModelSectionsDefaults: () => () => true },
    view: { ˆisView: () => () => true },
    "view.block": { ˆisViewBlock: () => () => true },
    "view.block.print": { ˆisViewBlockPrint: () => () => true },
    "view.block.update": { ˆisViewBlockUpdate: () => () => true },
    "view.find": { ˆisViewFind: () => () => true },
    "view.grid": { ˆisViewGrid: () => () => true },
    "view.grid.aggregate.*": { ˆisViewGridAggregateDefaults: () => () => true },
    "view.grid.style.*": { ˆisViewGridStyleDefaults: () => () => true },
} as const;

type DSLMethodsKeys = keyof typeof DSLMethods;

const DSLMethodPatterns = (Object.keys(DSLMethods) as [DSLMethodsKeys]).map(
    (methodKey) => {
        const pattern = methodKey
            .replace(/\./g, "\\.")
            .replace(/\*/g, "([^.]+)");
        const regex = new RegExp(`^${methodKey === "" ? "$" : `${pattern}$`}`);
        return { key: methodKey, regex };
    }
);

function calculateFieldNameFromPathAndType(
    type: DSLMethodsKeys,
    path: string,
    formName: string
): string | undefined {
    if (!type.includes("*")) return undefined;

    const typeParts = type.split(".");
    const pathRelativeToForm = path.substring(formName.length + 1);
    const pathParts = pathRelativeToForm.split(".");

    if (typeParts.length === pathParts.length) {
        for (let i = typeParts.length - 1; i >= 0; i--) {
            if (typeParts[i] === "*") {
                return pathParts[i];
            }
        }
    }
    return undefined;
}

export function dslFormDeep<S extends DSL>(
    obj: any,
    context: DSLContext<S>
): any {
    if (obj === null || typeof obj !== "object" || Array.isArray(obj)) {
        return obj;
    }

    const proxy = new Proxy(obj, {
        get(target, prop, receiver) {
            if (prop === "ˆparent") return context.parent ?? receiver;
            if (prop === "ˆpath") return context.path;
            if (prop === "ˆroot") return context.root ?? receiver;
            if (prop === "ˆtype") return context.type;

            const propStr = String(prop);

            const methodsForType = DSLMethods[context.type];
            const method = methodsForType?.[propStr];

            if (method) {
                const fieldNameForMethod = calculateFieldNameFromPathAndType(
                    context.type,
                    context.path,
                    context.formName
                );

                const storeKeyValue =
                    (context.type === "fields.*" ||
                        context.type.startsWith("fields.*.")) &&
                    context.parentObject
                        ? context.parentObject
                        : target;

                return method({
                    target: target,
                    storeKey: storeKeyValue,
                    root: (context.root ?? receiver) as DSLForm<S>,
                    parent: receiver,
                    dslFormDeep: dslFormDeep,
                    fieldName: fieldNameForMethod,
                    store: context.store,
                    formName: context.formName,
                });
            }

            const v = Reflect.get(target, prop, receiver);

            if (
                v &&
                typeof v === "object" &&
                prop !== "ˆroot" &&
                prop !== "ˆparent" &&
                prop !== "ˆpath" &&
                prop !== "ˆform"
            ) {
                const nextPath = `${context.path}.${propStr}`;
                const pathSegmentForChildTypeLookup = nextPath.substring(
                    context.formName.length + 1
                );

                let childType: string = "";
                for (const { key, regex } of DSLMethodPatterns) {
                    if (regex.test(pathSegmentForChildTypeLookup)) {
                        childType = key;
                        break;
                    }
                }

                return dslFormDeep<S>(v, {
                    path: nextPath,
                    type: childType,
                    store: context.store,
                    root: (context.root ?? proxy) as DSLForm<S>,
                    parent: proxy,
                    parentObject: target,
                    formName: context.formName,
                });
            }
            return v;
        },
        getOwnPropertyDescriptor(t, k) {
            return Reflect.getOwnPropertyDescriptor(t, k);
        },
    });

    if (!context.root) {
        proxy.ˆroot = proxy;
    }
    return proxy;
}

export function dslForm<S extends DSL>(formName: string, raw: S): DSLForm<S> {
    const store = new WeakMap<object, Record<string, unknown>>();
    const instance = dslFormDeep<S>(raw, {
        path: formName,
        type: "",
        store,
        root: undefined,
        parent: undefined,
        parentObject: undefined,
        formName,
    });
    instance.ˆparent = instance;
    return instance as DSLForm<S>;
}
